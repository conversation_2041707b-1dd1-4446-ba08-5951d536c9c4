<template>
  <div class="settings-page">

    <!-- 设置内容 -->
    <div class="settings-content">
      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- API配置 -->
        <el-tab-pane label="API配置" name="api">
          <el-card shadow="never">
            <template #header>
              <div class="card-header">
                <span>🔑 AI模型API配置</span>
                <el-button type="primary" @click="testAllConnections">测试所有连接</el-button>
              </div>
            </template>
            <ApiConfig />
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ApiConfig from '@/components/ApiConfig.vue'
import { apiConfigApi } from '@/services/apiConfigApi.js'

// 响应式数据
const activeTab = ref('api')

// 方法
const testAllConnections = async () => {
  try {
    ElMessage.info('正在获取API配置列表...')
    const configs = await apiConfigApi.getApiConfigs()
    const enabledConfigs = configs.filter(config => config.enabled && config.apiKey)

    if (enabledConfigs.length === 0) {
      ElMessage.warning('没有可测试的配置')
      return
    }

    ElMessage.info(`正在测试 ${enabledConfigs.length} 个配置的连接...`)

    let successCount = 0
    let failCount = 0

    for (const config of enabledConfigs) {
      try {
        await apiConfigApi.testApiConfig(config.id)
        successCount++
        ElMessage.success(`${config.name} 连接成功`)
      } catch (error) {
        failCount++
        ElMessage.error(`${config.name} 连接失败`)
      }
      // 间隔500ms避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    ElMessage.info(`测试完成：${successCount} 个成功，${failCount} 个失败`)
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('测试连接失败')
  }
}
</script>

<style scoped>
.settings-page {
  padding: 0;
}

.settings-content {
  background: white;
  border-radius: 8px;
}

.settings-tabs {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}


/* 主题样式 */
:root[data-theme="light"] {
  --bg-color: #ffffff;
  --text-color: #303133;
  --border-color: #e4e7ed;
}

:root[data-theme="dark"] {
  --bg-color: #1d1d1d;
  --text-color: #ffffff;
  --border-color: #434343;
}

:root[data-theme="dark"] .settings-page {
  background-color: var(--bg-color);
  color: var(--text-color);
}

:root[data-theme="dark"] .el-card {
  background-color: #2d2d2d;
  border-color: var(--border-color);
}

/* 禁用动画 */
.no-animations * {
  animation-duration: 0ms !important;
  animation-delay: 0ms !important;
  transition-duration: 0ms !important;
  transition-delay: 0ms !important;
}
</style>